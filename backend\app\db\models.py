from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, ForeignKey, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class UserRole(str, enum.Enum):
    STUDENT = "student"
    COMPANY = "company"
    ADMIN = "admin"

class ApplicationStatus(str, enum.Enum):
    PENDING = "pending"
    REVIEWED = "reviewed"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    role = Column(Enum(UserRole), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Student(Base):
    __tablename__ = "students"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    first_name = Column(String, nullable=False)
    last_name = Column(String, nullable=False)
    university = Column(String)
    degree = Column(String)
    graduation_year = Column(Integer)
    gpa = Column(Float)
    skills = Column(JSON)  # List of skills
    interests = Column(JSON)  # Areas of interest
    experience = Column(Text)
    resume_url = Column(String)
    linkedin_url = Column(String)
    github_url = Column(String)
    portfolio_url = Column(String)
    location_preference = Column(JSON)  # Preferred locations
    salary_expectation = Column(Float)
    availability_date = Column(DateTime)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    user = relationship("User", backref="student_profile")
    applications = relationship("Application", back_populates="student")

class Company(Base):
    __tablename__ = "companies"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    industry = Column(String)
    size = Column(String)  # startup, small, medium, large
    location = Column(String)
    website = Column(String)
    culture_values = Column(JSON)  # Company culture and values
    benefits = Column(JSON)  # Benefits offered
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    user = relationship("User", backref="company_profile")
    internships = relationship("Internship", back_populates="company")

class Internship(Base):
    __tablename__ = "internships"
    
    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"))
    title = Column(String, nullable=False)
    description = Column(Text)
    requirements = Column(JSON)  # Required skills and qualifications
    preferred_skills = Column(JSON)  # Nice-to-have skills
    responsibilities = Column(Text)
    duration_months = Column(Integer)
    stipend = Column(Float)
    location = Column(String)
    is_remote = Column(Boolean, default=False)
    start_date = Column(DateTime)
    application_deadline = Column(DateTime)
    is_active = Column(Boolean, default=True)
    max_applicants = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    company = relationship("Company", back_populates="internships")
    applications = relationship("Application", back_populates="internship")

class Application(Base):
    __tablename__ = "applications"
    
    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    internship_id = Column(Integer, ForeignKey("internships.id"))
    status = Column(Enum(ApplicationStatus), default=ApplicationStatus.PENDING)
    cover_letter = Column(Text)
    additional_documents = Column(JSON)  # URLs to additional documents
    applied_at = Column(DateTime(timezone=True), server_default=func.now())
    reviewed_at = Column(DateTime(timezone=True))
    feedback = Column(Text)
    
    student = relationship("Student", back_populates="applications")
    internship = relationship("Internship", back_populates="applications")

class Placement(Base):
    __tablename__ = "placements"
    
    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    internship_id = Column(Integer, ForeignKey("internships.id"))
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    performance_rating = Column(Float)  # 1-5 scale
    feedback = Column(Text)
    skills_gained = Column(JSON)
    mentor_rating = Column(Float)
    would_recommend = Column(Boolean)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class Recommendation(Base):
    __tablename__ = "recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    internship_id = Column(Integer, ForeignKey("internships.id"))
    score = Column(Float)  # Recommendation confidence score
    reasons = Column(JSON)  # Reasons for recommendation
    skill_match_score = Column(Float)
    culture_match_score = Column(Float)
    location_match_score = Column(Float)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class SkillGap(Base):
    __tablename__ = "skill_gaps"
    
    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    internship_id = Column(Integer, ForeignKey("internships.id"))
    missing_skills = Column(JSON)
    learning_resources = Column(JSON)
    estimated_learning_time = Column(Integer)  # in hours
    priority_score = Column(Float)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
