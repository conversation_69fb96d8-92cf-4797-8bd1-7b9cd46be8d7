# AI-Based Internship Recommendation Engine for PM Internship Scheme

## Overview
An intelligent recommendation system that matches students with Product Manager internship opportunities using machine learning algorithms and comprehensive profile analysis.

## Features
- **Student Profile Matching**: AI-powered matching based on skills, education, and preferences
- **Company/Role Recommendations**: Intelligent suggestions for both students and companies
- **Skill Gap Analysis**: Identifies areas for improvement and suggests learning paths
- **Application Tracking**: Complete application lifecycle management
- **Performance Analytics**: Data-driven insights on placement success and trends

## Technology Stack
- **Backend**: Python (FastAPI)
- **Frontend**: React.js
- **Database**: PostgreSQL
- **AI/ML**: scikit-learn, pandas, numpy
- **Authentication**: JWT tokens
- **Deployment**: Docker

## Project Structure
```
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── db/             # Database models and connections
│   │   ├── ml/             # Machine learning models
│   │   └── services/       # Business logic
│   ├── tests/              # Backend tests
│   └── requirements.txt    # Python dependencies
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   └── package.json        # Node.js dependencies
├── data/                   # Sample data and datasets
├── docs/                   # Documentation
└── docker-compose.yml      # Docker configuration
```

## Data Models
- **Students**: Profile, skills, education, preferences
- **Companies**: Profile, culture, requirements
- **Internships**: Opportunities, requirements, benefits
- **Applications**: Tracking and status management
- **Placements**: Historical data for ML training

## Getting Started
1. Clone the repository
2. Set up the backend (Python environment)
3. Set up the frontend (Node.js environment)
4. Configure the database
5. Run the application

## API Endpoints
- `/api/students/` - Student management
- `/api/companies/` - Company management
- `/api/internships/` - Internship opportunities
- `/api/recommendations/` - AI recommendations
- `/api/applications/` - Application tracking
- `/api/analytics/` - Performance metrics

## Machine Learning Features
- **Collaborative Filtering**: Based on historical placements
- **Content-Based Filtering**: Skills and requirements matching
- **Hybrid Approach**: Combines multiple recommendation strategies
- **Skill Gap Analysis**: Identifies missing skills and suggests improvements
