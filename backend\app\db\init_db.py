from sqlalchemy.orm import Session
from app.db.base import engine, Base
from app.db.models import User, Student, Company
from app.core.security import get_password_hash
from app.db.base import SessionLocal

def init_db() -> None:
    """Initialize database with tables and sample data"""
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    # Create sample data
    db = SessionLocal()
    try:
        # Check if admin user exists
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                role="admin"
            )
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            
        # Create sample student
        student_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not student_user:
            student_user = User(
                email="<EMAIL>",
                hashed_password=get_password_hash("student123"),
                role="student"
            )
            db.add(student_user)
            db.commit()
            db.refresh(student_user)
            
            # Create student profile
            student_profile = Student(
                user_id=student_user.id,
                first_name="John",
                last_name="Doe",
                university="Tech University",
                degree="Computer Science",
                graduation_year=2024,
                gpa=3.8,
                skills=["Python", "JavaScript", "React", "SQL", "Machine Learning"],
                interests=["Product Management", "AI", "Startups"],
                experience="Internship at Tech Startup, Personal Projects",
                location_preference=["San Francisco", "New York", "Remote"],
                salary_expectation=5000.0
            )
            db.add(student_profile)
            
        # Create sample company
        company_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not company_user:
            company_user = User(
                email="<EMAIL>",
                hashed_password=get_password_hash("company123"),
                role="company"
            )
            db.add(company_user)
            db.commit()
            db.refresh(company_user)
            
            # Create company profile
            company_profile = Company(
                user_id=company_user.id,
                name="TechCorp Inc.",
                description="Leading technology company focused on AI and ML solutions",
                industry="Technology",
                size="medium",
                location="San Francisco, CA",
                website="https://techcorp.com",
                culture_values=["Innovation", "Collaboration", "Growth", "Impact"],
                benefits=["Health Insurance", "Flexible Hours", "Learning Budget", "Remote Work"]
            )
            db.add(company_profile)
            
        db.commit()
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        db.rollback()
    finally:
        db.close()
